<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大老二遊戲測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>大老二遊戲測試頁面</h1>
    
    <div class="test-section">
        <h2>基礎功能測試</h2>
        <button onclick="runBasicTests()">運行基礎測試</button>
        <div id="basic-test-results"></div>
    </div>

    <div class="test-section">
        <h2>牌型分析測試</h2>
        <button onclick="runHandAnalysisTests()">運行牌型測試</button>
        <div id="hand-test-results"></div>
    </div>

    <div class="test-section">
        <h2>遊戲邏輯測試</h2>
        <button onclick="runGameLogicTests()">運行遊戲邏輯測試</button>
        <div id="game-test-results"></div>
    </div>

    <div class="test-section">
        <h2>快速遊戲演示</h2>
        <button onclick="runGameDemo()">運行遊戲演示</button>
        <div id="demo-results"></div>
    </div>

    <!-- 引入遊戲腳本 -->
    <script src="js/card.js"></script>
    <script src="js/deck.js"></script>
    <script src="js/player.js"></script>
    <script src="js/game.js"></script>

    <script>
        function addTestResult(containerId, testName, passed, message = '') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'pass' : 'fail'}`;
            resultDiv.innerHTML = `
                <strong>${testName}:</strong> ${passed ? '✓ 通過' : '✗ 失敗'}
                ${message ? `<br><small>${message}</small>` : ''}
            `;
            container.appendChild(resultDiv);
        }

        function runBasicTests() {
            const container = document.getElementById('basic-test-results');
            container.innerHTML = '';

            try {
                // 測試Card類
                const card1 = new Card('hearts', 14); // A♥
                const card2 = new Card('spades', 15); // 2♠
                
                addTestResult('basic-test-results', 'Card創建', true, `創建了${card1.toString()}和${card2.toString()}`);
                
                // 測試牌的比較
                const comparison = card2.isGreaterThan(card1);
                addTestResult('basic-test-results', '牌的比較', comparison, '2♠ > A♥');

                // 測試Deck類
                const deck = new Deck();
                addTestResult('basic-test-results', 'Deck創建', deck.getCardCount() === 52, `牌組有${deck.getCardCount()}張牌`);

                // 測試發牌
                const hand = deck.dealCards(13);
                addTestResult('basic-test-results', '發牌功能', hand.length === 13, `發了${hand.length}張牌`);

                // 測試Player類
                const player = new Player('測試玩家');
                player.receiveCards(hand);
                addTestResult('basic-test-results', 'Player創建', player.getCardCount() === 13, `玩家有${player.getCardCount()}張牌`);

            } catch (error) {
                addTestResult('basic-test-results', '基礎測試', false, error.message);
            }
        }

        function runHandAnalysisTests() {
            const container = document.getElementById('hand-test-results');
            container.innerHTML = '';

            try {
                // 測試單張
                const singleCard = [new Card('hearts', 14)];
                const singleAnalysis = HandAnalyzer.analyzeHand(singleCard);
                addTestResult('hand-test-results', '單張分析', singleAnalysis.type === 'single', `識別為: ${singleAnalysis.type}`);

                // 測試對子
                const pair = [new Card('hearts', 10), new Card('spades', 10)];
                const pairAnalysis = HandAnalyzer.analyzeHand(pair);
                addTestResult('hand-test-results', '對子分析', pairAnalysis.type === 'pair', `識別為: ${pairAnalysis.type}`);

                // 測試三條
                const triple = [new Card('hearts', 7), new Card('spades', 7), new Card('clubs', 7)];
                const tripleAnalysis = HandAnalyzer.analyzeHand(triple);
                addTestResult('hand-test-results', '三條分析', tripleAnalysis.type === 'triple', `識別為: ${tripleAnalysis.type}`);

                // 測試順子
                const straight = [
                    new Card('hearts', 3),
                    new Card('spades', 4),
                    new Card('clubs', 5),
                    new Card('diamonds', 6),
                    new Card('hearts', 7)
                ];
                const straightAnalysis = HandAnalyzer.analyzeHand(straight);
                addTestResult('hand-test-results', '順子分析', straightAnalysis.type === 'straight', `識別為: ${straightAnalysis.type}`);

                // 測試同花
                const flush = [
                    new Card('hearts', 3),
                    new Card('hearts', 7),
                    new Card('hearts', 9),
                    new Card('hearts', 11),
                    new Card('hearts', 13)
                ];
                const flushAnalysis = HandAnalyzer.analyzeHand(flush);
                addTestResult('hand-test-results', '同花分析', flushAnalysis.type === 'flush', `識別為: ${flushAnalysis.type}`);

            } catch (error) {
                addTestResult('hand-test-results', '牌型分析測試', false, error.message);
            }
        }

        function runGameLogicTests() {
            const container = document.getElementById('game-test-results');
            container.innerHTML = '';

            try {
                // 創建遊戲
                const game = new BigTwoGame();
                game.initGame();
                
                addTestResult('game-test-results', '遊戲初始化', true, '遊戲成功創建並初始化');

                // 檢查遊戲狀態
                const gameState = game.getGameState();
                addTestResult('game-test-results', '遊戲狀態', gameState.gameState === 'playing', `狀態: ${gameState.gameState}`);

                // 檢查玩家數量
                addTestResult('game-test-results', '玩家數量', gameState.players.length === 4, `有${gameState.players.length}個玩家`);

                // 檢查手牌分配
                const totalCards = gameState.players.reduce((sum, player) => sum + player.cardCount, 0);
                addTestResult('game-test-results', '手牌分配', totalCards === 52, `總共分配了${totalCards}張牌`);

                // 檢查♦3
                let hasDiamondThree = false;
                for (let i = 0; i < game.players.length; i++) {
                    if (game.players[i].hasDiamondThree()) {
                        hasDiamondThree = true;
                        addTestResult('game-test-results', '♦3檢查', true, `玩家${i + 1}有♦3`);
                        break;
                    }
                }
                if (!hasDiamondThree) {
                    addTestResult('game-test-results', '♦3檢查', false, '沒有玩家有♦3');
                }

            } catch (error) {
                addTestResult('game-test-results', '遊戲邏輯測試', false, error.message);
            }
        }

        function runGameDemo() {
            const container = document.getElementById('demo-results');
            container.innerHTML = '';

            try {
                const game = new BigTwoGame();
                game.initGame();

                addTestResult('demo-results', '演示開始', true, '創建新遊戲');

                // 找到有♦3的玩家
                let diamondThreePlayer = -1;
                for (let i = 0; i < game.players.length; i++) {
                    if (game.players[i].hasDiamondThree()) {
                        diamondThreePlayer = i;
                        break;
                    }
                }

                if (diamondThreePlayer !== -1) {
                    const player = game.players[diamondThreePlayer];
                    const diamondThreeIndex = player.getDiamondThreeIndex();
                    
                    addTestResult('demo-results', '找到♦3', true, `玩家${diamondThreePlayer + 1}有♦3`);

                    // 嘗試出♦3
                    const result = game.playCards(diamondThreePlayer, [diamondThreeIndex]);
                    addTestResult('demo-results', '出♦3', result.success, result.message);

                    if (result.success) {
                        const gameState = game.getGameState();
                        addTestResult('demo-results', '遊戲繼續', true, `下一個玩家: ${gameState.players[gameState.currentPlayer].name}`);
                    }
                }

            } catch (error) {
                addTestResult('demo-results', '遊戲演示', false, error.message);
            }
        }

        // 頁面加載時顯示歡迎信息
        window.addEventListener('load', function() {
            console.log('大老二遊戲測試頁面已加載');
        });
    </script>
</body>
</html>
