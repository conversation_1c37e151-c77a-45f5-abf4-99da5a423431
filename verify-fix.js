// 驗證修復的腳本 - 在瀏覽器控制台運行

function verifyFix() {
    console.log('=== 驗證黑桃2修復 ===');
    
    try {
        // 創建遊戲
        const game = new BigTwoGame();
        game.initGame();
        console.log('✓ 遊戲初始化成功');
        
        // 檢查初始狀態
        let state = game.getGameState();
        console.log(`初始狀態: isFirstRound=${state.isFirstRound}, isGameStart=${state.isGameStart}`);
        
        // 找到有♦3的玩家並出牌
        let diamondThreePlayer = -1;
        for (let i = 0; i < game.players.length; i++) {
            if (game.players[i].hasDiamondThree()) {
                diamondThreePlayer = i;
                break;
            }
        }
        
        // 出♦3
        const player = game.players[diamondThreePlayer];
        const diamondThreeIndex = player.getDiamondThreeIndex();
        const playResult = game.playCards(diamondThreePlayer, [diamondThreeIndex]);
        console.log(`✓ 出♦3: ${playResult.success ? '成功' : '失敗'}`);
        
        // 檢查狀態變化
        state = game.getGameState();
        console.log(`出牌後: isFirstRound=${state.isFirstRound}, isGameStart=${state.isGameStart}`);
        
        // 給下一個玩家一張大牌（模擬黑桃2）
        const nextPlayer = game.getCurrentPlayer();
        const bigCard = new Card('spades', 15); // 黑桃2
        nextPlayer.receiveCard(bigCard);
        
        // 出大牌
        const bigCardIndex = nextPlayer.hand.length - 1;
        const bigCardResult = game.playCards(game.currentPlayerIndex, [bigCardIndex]);
        console.log(`✓ 出大牌: ${bigCardResult.success ? '成功' : '失敗'}`);
        
        // 三個玩家pass
        console.log('--- 模擬三個玩家pass ---');
        for (let i = 0; i < 3; i++) {
            const passResult = game.pass(game.currentPlayerIndex);
            console.log(`玩家${game.currentPlayerIndex + 1} pass: ${passResult.success ? '成功' : '失敗'}`);
        }
        
        // 檢查最終狀態
        state = game.getGameState();
        console.log('--- 最終狀態 ---');
        console.log(`場面清空: ${!state.lastPlay}`);
        console.log(`isFirstRound: ${state.isFirstRound}`);
        console.log(`isGameStart: ${state.isGameStart}`);
        console.log(`當前玩家: ${state.currentPlayer + 1}`);
        
        // 關鍵測試：檢查是否可以出任意牌
        if (state.isFirstRound && !state.isGameStart) {
            console.log('✓ 正確狀態：場面清空但不是遊戲開始');
            
            // 測試驗證邏輯
            const testPlayer = game.getCurrentPlayer();
            if (testPlayer.getCardCount() > 0) {
                // 找一張不是♦3的牌
                let nonDiamond3Index = -1;
                for (let i = 0; i < testPlayer.hand.length; i++) {
                    if (!testPlayer.hand[i].isDiamondThree()) {
                        nonDiamond3Index = i;
                        break;
                    }
                }
                
                if (nonDiamond3Index !== -1) {
                    const validation = game.validatePlay(testPlayer, [nonDiamond3Index]);
                    console.log(`✓ 可以出非♦3牌: ${validation.valid ? '是' : '否'}`);
                    if (!validation.valid) {
                        console.log(`驗證失敗原因: ${validation.message}`);
                    }
                } else {
                    console.log('! 玩家只有♦3，無法測試非♦3牌');
                }
            }
        } else {
            console.log('✗ 狀態錯誤：應該是場面清空但不是遊戲開始');
        }
        
    } catch (error) {
        console.log(`✗ 測試失敗: ${error.message}`);
        console.error(error);
    }
    
    console.log('=== 驗證完成 ===');
}

// 如果在瀏覽器環境中，等待頁面加載完成後運行
if (typeof window !== 'undefined') {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', verifyFix);
    } else {
        verifyFix();
    }
}

// 導出函數
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { verifyFix };
}
