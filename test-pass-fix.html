<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pass修復測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .game-state {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Pass修復測試</h1>
    
    <div class="test-section">
        <h2>模擬Pass場景測試</h2>
        <button onclick="testPassScenario()">測試Pass場景</button>
        <div id="pass-test-results"></div>
    </div>

    <div class="test-section">
        <h2>AI邏輯測試</h2>
        <button onclick="testAILogic()">測試AI邏輯</button>
        <div id="ai-test-results"></div>
    </div>

    <!-- 引入遊戲腳本 -->
    <script src="js/card.js"></script>
    <script src="js/deck.js"></script>
    <script src="js/player.js"></script>
    <script src="js/game.js"></script>

    <script>
        function addTestResult(containerId, testName, passed, message = '') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'pass' : 'fail'}`;
            resultDiv.innerHTML = `
                <strong>${testName}:</strong> ${passed ? '✓ 通過' : '✗ 失敗'}
                ${message ? `<br><small>${message}</small>` : ''}
            `;
            container.appendChild(resultDiv);
        }

        function addInfoResult(containerId, message) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = `<strong>信息:</strong> ${message}`;
            container.appendChild(resultDiv);
        }

        function addGameState(containerId, gameState) {
            const container = document.getElementById(containerId);
            const stateDiv = document.createElement('div');
            stateDiv.className = 'game-state';
            stateDiv.innerHTML = `
                <strong>遊戲狀態:</strong><br>
                當前玩家: ${gameState.currentPlayer} (${gameState.players[gameState.currentPlayer].name})<br>
                遊戲狀態: ${gameState.gameState}<br>
                是否第一輪: ${gameState.isFirstRound}<br>
                上一手牌: ${gameState.lastPlay ? '有' : '無'}<br>
                ${gameState.lastPlay ? `上一手出牌者: ${gameState.players[gameState.lastPlay.player].name}` : ''}
            `;
            container.appendChild(stateDiv);
        }

        function testPassScenario() {
            const container = document.getElementById('pass-test-results');
            container.innerHTML = '';

            try {
                // 創建遊戲
                const game = new BigTwoGame();
                game.initGame();
                
                addTestResult('pass-test-results', '遊戲初始化', true, '遊戲成功創建');
                addGameState('pass-test-results', game.getGameState());

                // 找到有♦3的玩家並出牌
                let diamondThreePlayer = -1;
                for (let i = 0; i < game.players.length; i++) {
                    if (game.players[i].hasDiamondThree()) {
                        diamondThreePlayer = i;
                        break;
                    }
                }

                if (diamondThreePlayer !== -1) {
                    const player = game.players[diamondThreePlayer];
                    const diamondThreeIndex = player.getDiamondThreeIndex();
                    
                    // 出♦3
                    const playResult = game.playCards(diamondThreePlayer, [diamondThreeIndex]);
                    addTestResult('pass-test-results', '出♦3', playResult.success, playResult.message);
                    addGameState('pass-test-results', game.getGameState());

                    if (playResult.success) {
                        // 模擬其他三個玩家都pass
                        let passCount = 0;
                        for (let i = 0; i < 3; i++) {
                            const currentPlayer = game.getCurrentPlayer();
                            if (currentPlayer.isAI) {
                                const passResult = game.pass(game.currentPlayerIndex);
                                passCount++;
                                addInfoResult('pass-test-results', `玩家${game.currentPlayerIndex + 1} Pass (${passCount}/3)`);
                                
                                if (passResult.success) {
                                    addGameState('pass-test-results', game.getGameState());
                                } else {
                                    addTestResult('pass-test-results', `Pass ${i+1}`, false, passResult.message);
                                    break;
                                }
                            }
                        }

                        // 檢查場面是否清空
                        const finalState = game.getGameState();
                        const fieldCleared = !finalState.lastPlay;
                        addTestResult('pass-test-results', '場面清空', fieldCleared, fieldCleared ? '場面已清空' : '場面未清空');
                        
                        // 檢查當前玩家是否正確
                        const correctPlayer = finalState.currentPlayer === diamondThreePlayer;
                        addTestResult('pass-test-results', '當前玩家正確', correctPlayer, 
                            `當前玩家: ${finalState.currentPlayer}, 預期: ${diamondThreePlayer}`);

                        // 測試AI是否能繼續出牌
                        if (fieldCleared && correctPlayer) {
                            const currentAI = game.getCurrentPlayer();
                            if (currentAI.isAI) {
                                try {
                                    const aiMove = currentAI.makeMove(null, finalState);
                                    addTestResult('pass-test-results', 'AI能繼續出牌', aiMove.action === 'play', 
                                        `AI決定: ${aiMove.action}`);
                                } catch (error) {
                                    addTestResult('pass-test-results', 'AI能繼續出牌', false, error.message);
                                }
                            }
                        }

                        addGameState('pass-test-results', game.getGameState());
                    }
                }

            } catch (error) {
                addTestResult('pass-test-results', 'Pass場景測試', false, error.message);
            }
        }

        function testAILogic() {
            const container = document.getElementById('ai-test-results');
            container.innerHTML = '';

            try {
                // 測試AI在不同情況下的決策
                const ai = new AIPlayer('測試AI');
                
                // 給AI一些牌
                const testCards = [
                    new Card('hearts', 3),
                    new Card('spades', 7),
                    new Card('clubs', 10),
                    new Card('diamonds', 13),
                    new Card('hearts', 15)
                ];
                ai.receiveCards(testCards);

                addTestResult('ai-test-results', 'AI創建', true, `AI有${ai.getCardCount()}張牌`);

                // 測試場面清空時的決策（無♦3）
                const gameState1 = {
                    isFirstRound: false,
                    gameState: 'playing'
                };
                
                const move1 = ai.makeMove(null, gameState1);
                addTestResult('ai-test-results', '場面清空時出牌', move1.action === 'play', 
                    `AI決定: ${move1.action}, 牌: ${move1.cards || 'N/A'}`);

                // 測試真正第一輪但沒有♦3的情況
                const gameState2 = {
                    isFirstRound: true,
                    gameState: 'playing'
                };

                const move2 = ai.makeMove(null, gameState2);
                addTestResult('ai-test-results', '第一輪無♦3', move2.action === 'play', 
                    `AI決定: ${move2.action}, 牌: ${move2.cards || 'N/A'}`);

                // 給AI添加♦3並測試
                ai.receiveCard(new Card('diamonds', 3));
                const move3 = ai.makeMove(null, gameState2);
                addTestResult('ai-test-results', '第一輪有♦3', move3.action === 'play', 
                    `AI決定: ${move3.action}, 牌: ${move3.cards || 'N/A'}`);

            } catch (error) {
                addTestResult('ai-test-results', 'AI邏輯測試', false, error.message);
            }
        }

        // 頁面加載時顯示歡迎信息
        window.addEventListener('load', function() {
            console.log('Pass修復測試頁面已加載');
        });
    </script>
</body>
</html>
