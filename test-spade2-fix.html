<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>黑桃2修復測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .game-state {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>黑桃2修復測試</h1>
    
    <div class="test-section">
        <h2>測試場面清空後可出任意牌</h2>
        <button onclick="testSpade2Scenario()">測試黑桃2場景</button>
        <div id="spade2-test-results"></div>
    </div>

    <!-- 引入遊戲腳本 -->
    <script src="js/card.js"></script>
    <script src="js/deck.js"></script>
    <script src="js/player.js"></script>
    <script src="js/game.js"></script>

    <script>
        function addTestResult(containerId, testName, passed, message = '') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'pass' : 'fail'}`;
            resultDiv.innerHTML = `
                <strong>${testName}:</strong> ${passed ? '✓ 通過' : '✗ 失敗'}
                ${message ? `<br><small>${message}</small>` : ''}
            `;
            container.appendChild(resultDiv);
        }

        function addInfoResult(containerId, message) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = `<strong>信息:</strong> ${message}`;
            container.appendChild(resultDiv);
        }

        function addGameState(containerId, gameState) {
            const container = document.getElementById(containerId);
            const stateDiv = document.createElement('div');
            stateDiv.className = 'game-state';
            stateDiv.innerHTML = `
                <strong>遊戲狀態:</strong><br>
                當前玩家: ${gameState.currentPlayer + 1} (${gameState.players[gameState.currentPlayer].name})<br>
                遊戲狀態: ${gameState.gameState}<br>
                是否第一輪: ${gameState.isFirstRound}<br>
                是否遊戲開始: ${gameState.isGameStart}<br>
                上一手牌: ${gameState.lastPlay ? '有' : '無'}<br>
                ${gameState.lastPlay ? `上一手出牌者: ${gameState.players[gameState.lastPlay.player].name}` : ''}
            `;
            container.appendChild(stateDiv);
        }

        function testSpade2Scenario() {
            const container = document.getElementById('spade2-test-results');
            container.innerHTML = '';

            try {
                // 創建遊戲
                const game = new BigTwoGame();
                game.initGame();
                
                addTestResult('spade2-test-results', '遊戲初始化', true, '遊戲成功創建');
                addGameState('spade2-test-results', game.getGameState());

                // 找到有♦3的玩家並出牌
                let diamondThreePlayer = -1;
                for (let i = 0; i < game.players.length; i++) {
                    if (game.players[i].hasDiamondThree()) {
                        diamondThreePlayer = i;
                        break;
                    }
                }

                if (diamondThreePlayer !== -1) {
                    const player = game.players[diamondThreePlayer];
                    const diamondThreeIndex = player.getDiamondThreeIndex();
                    
                    // 出♦3
                    const playResult = game.playCards(diamondThreePlayer, [diamondThreeIndex]);
                    addTestResult('spade2-test-results', '出♦3', playResult.success, playResult.message);
                    addGameState('spade2-test-results', game.getGameState());

                    if (playResult.success) {
                        // 給當前玩家添加黑桃2
                        const currentPlayer = game.getCurrentPlayer();
                        const spade2 = new Card('spades', 15); // 黑桃2
                        currentPlayer.receiveCard(spade2);
                        
                        addInfoResult('spade2-test-results', `給${currentPlayer.name}添加黑桃2`);
                        
                        // 出黑桃2
                        const spade2Index = currentPlayer.hand.length - 1; // 最後一張牌
                        const spade2Result = game.playCards(game.currentPlayerIndex, [spade2Index]);
                        addTestResult('spade2-test-results', '出黑桃2', spade2Result.success, spade2Result.message);
                        addGameState('spade2-test-results', game.getGameState());

                        if (spade2Result.success) {
                            // 模擬其他三個玩家都pass
                            let passCount = 0;
                            for (let i = 0; i < 3; i++) {
                                const passResult = game.pass(game.currentPlayerIndex);
                                passCount++;
                                addInfoResult('spade2-test-results', `玩家${game.currentPlayerIndex + 1} Pass (${passCount}/3)`);
                                
                                if (passResult.success) {
                                    addGameState('spade2-test-results', game.getGameState());
                                } else {
                                    addTestResult('spade2-test-results', `Pass ${i+1}`, false, passResult.message);
                                    break;
                                }
                            }

                            // 檢查場面是否清空
                            const finalState = game.getGameState();
                            const fieldCleared = !finalState.lastPlay;
                            addTestResult('spade2-test-results', '場面清空', fieldCleared, fieldCleared ? '場面已清空' : '場面未清空');
                            
                            // 檢查是否不再要求♦3
                            const notRequireDiamond3 = finalState.isFirstRound && !finalState.isGameStart;
                            addTestResult('spade2-test-results', '不要求♦3', notRequireDiamond3, 
                                `isFirstRound: ${finalState.isFirstRound}, isGameStart: ${finalState.isGameStart}`);

                            // 測試能否出任意牌
                            if (fieldCleared && notRequireDiamond3) {
                                const currentAI = game.getCurrentPlayer();
                                if (currentAI.isAI && currentAI.getCardCount() > 0) {
                                    // 嘗試出第一張牌（不是♦3）
                                    const firstCardIndex = 0;
                                    const firstCard = currentAI.hand[firstCardIndex];
                                    const isDiamond3 = firstCard.isDiamondThree();
                                    
                                    if (!isDiamond3) {
                                        const anyCardResult = game.playCards(game.currentPlayerIndex, [firstCardIndex]);
                                        addTestResult('spade2-test-results', '能出任意牌', anyCardResult.success, 
                                            `出牌: ${firstCard.toString()}, 結果: ${anyCardResult.message}`);
                                    } else {
                                        addInfoResult('spade2-test-results', 'AI的第一張牌恰好是♦3，無法測試非♦3牌');
                                    }
                                }
                            }

                            addGameState('spade2-test-results', game.getGameState());
                        }
                    }
                }

            } catch (error) {
                addTestResult('spade2-test-results', '黑桃2場景測試', false, error.message);
                console.error(error);
            }
        }

        // 頁面加載時顯示歡迎信息
        window.addEventListener('load', function() {
            console.log('黑桃2修復測試頁面已加載');
        });
    </script>
</body>
</html>
