// 快速測試腳本 - 在瀏覽器控制台中運行

function quickTest() {
    console.log('=== 開始快速測試 ===');
    
    try {
        // 創建遊戲
        const game = new BigTwoGame();
        game.initGame();
        console.log('✓ 遊戲初始化成功');
        
        // 找到有♦3的玩家
        let diamondThreePlayer = -1;
        for (let i = 0; i < game.players.length; i++) {
            if (game.players[i].hasDiamondThree()) {
                diamondThreePlayer = i;
                break;
            }
        }
        
        console.log(`✓ 找到♦3玩家: 玩家${diamondThreePlayer + 1}`);
        
        // 出♦3
        const player = game.players[diamondThreePlayer];
        const diamondThreeIndex = player.getDiamondThreeIndex();
        const playResult = game.playCards(diamondThreePlayer, [diamondThreeIndex]);
        
        if (playResult.success) {
            console.log('✓ 成功出♦3');
            console.log(`當前玩家: ${game.currentPlayerIndex + 1}`);
            console.log(`上一手出牌者: ${game.lastPlayerIndex + 1}`);
            
            // 模擬三個玩家pass
            console.log('--- 開始模擬pass ---');
            for (let i = 0; i < 3; i++) {
                const passResult = game.pass(game.currentPlayerIndex);
                if (passResult.success) {
                    console.log(`✓ 玩家${game.currentPlayerIndex + 1} Pass成功`);
                } else {
                    console.log(`✗ Pass失敗: ${passResult.message}`);
                    break;
                }
            }
            
            // 檢查最終狀態
            const finalState = game.getGameState();
            console.log('--- 最終狀態 ---');
            console.log(`場面是否清空: ${!finalState.lastPlay}`);
            console.log(`當前玩家: ${finalState.currentPlayer + 1}`);
            console.log(`是否第一輪: ${finalState.isFirstRound}`);
            console.log(`遊戲狀態: ${finalState.gameState}`);
            
            // 測試AI是否能繼續
            if (!finalState.lastPlay && finalState.gameState === 'playing') {
                const currentAI = game.getCurrentPlayer();
                if (currentAI.isAI) {
                    try {
                        const aiMove = currentAI.makeMove(null, finalState);
                        console.log(`✓ AI能繼續出牌: ${aiMove.action}`);
                        
                        if (aiMove.action === 'play') {
                            const aiPlayResult = game.playCards(game.currentPlayerIndex, aiMove.cards);
                            console.log(`AI出牌結果: ${aiPlayResult.success ? '成功' : '失敗'}`);
                        }
                    } catch (error) {
                        console.log(`✗ AI出牌錯誤: ${error.message}`);
                    }
                }
            }
            
        } else {
            console.log(`✗ 出♦3失敗: ${playResult.message}`);
        }
        
    } catch (error) {
        console.log(`✗ 測試失敗: ${error.message}`);
        console.error(error);
    }
    
    console.log('=== 測試完成 ===');
}

// 如果在瀏覽器環境中，自動運行測試
if (typeof window !== 'undefined') {
    // 等待頁面加載完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', quickTest);
    } else {
        quickTest();
    }
}

// 導出函數以便手動調用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { quickTest };
}
