/**
 * 主程序入口
 * 初始化遊戲和UI
 */

// 全局變量
let game;
let ui;

/**
 * 頁面加載完成後初始化遊戲
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeGame();
});

/**
 * 初始化遊戲
 */
function initializeGame() {
    try {
        // 創建遊戲實例
        game = new BigTwoGame();
        
        // 創建UI控制器
        ui = new UIController(game);
        
        // 開始新遊戲
        game.initGame();
        
        // 更新UI
        ui.updateUI();
        
        // 如果第一個玩家是AI，開始AI回合
        ui.handleAITurn();
        
        console.log('大老二遊戲初始化完成');
        
    } catch (error) {
        console.error('遊戲初始化失敗:', error);
        showErrorMessage('遊戲初始化失敗，請刷新頁面重試');
    }
}

/**
 * 顯示錯誤消息
 */
function showErrorMessage(message) {
    const errorElement = document.createElement('div');
    errorElement.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #dc3545;
        color: white;
        padding: 20px;
        border-radius: 10px;
        font-size: 18px;
        font-weight: bold;
        z-index: 9999;
        text-align: center;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    `;
    errorElement.textContent = message;
    
    document.body.appendChild(errorElement);
    
    // 5秒後移除
    setTimeout(() => {
        if (errorElement.parentNode) {
            errorElement.parentNode.removeChild(errorElement);
        }
    }, 5000);
}

/**
 * 處理頁面可見性變化
 * 當頁面重新變為可見時，確保遊戲狀態正確
 */
document.addEventListener('visibilitychange', function() {
    if (!document.hidden && game && ui) {
        // 頁面重新可見時更新UI
        ui.updateUI();
    }
});

/**
 * 處理窗口大小變化
 */
window.addEventListener('resize', function() {
    // 可以在這裡添加響應式布局調整
    console.log('窗口大小已改變');
});

/**
 * 鍵盤快捷鍵
 */
document.addEventListener('keydown', function(event) {
    if (!game || !ui) return;
    
    const gameState = game.getGameState();
    const isPlayerTurn = gameState.currentPlayer === 0 && gameState.gameState === 'playing';
    
    if (!isPlayerTurn) return;
    
    switch (event.key) {
        case 'Enter':
            // 回車鍵出牌
            if (ui.selectedCards.size > 0) {
                event.preventDefault();
                ui.playSelectedCards();
            }
            break;
            
        case ' ':
        case 'Spacebar':
            // 空格鍵Pass
            if (!gameState.isFirstRound && gameState.lastPlay) {
                event.preventDefault();
                ui.pass();
            }
            break;
            
        case 'Escape':
            // ESC鍵取消選擇
            event.preventDefault();
            ui.selectedCards.clear();
            ui.updatePlayer1Cards();
            ui.updateButtons();
            break;
            
        case 'r':
        case 'R':
            // R鍵重新開始
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                ui.newGame();
            }
            break;
            
        case 'h':
        case 'H':
        case '?':
            // H鍵或?鍵顯示規則
            event.preventDefault();
            ui.showRules();
            break;
    }
});

/**
 * 防止頁面意外刷新時丟失遊戲進度
 */
window.addEventListener('beforeunload', function(event) {
    if (game && game.getGameState().gameState === 'playing') {
        const message = '遊戲正在進行中，確定要離開嗎？';
        event.returnValue = message;
        return message;
    }
});

/**
 * 調試功能（僅在開發環境中使用）
 */
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    // 添加調試快捷鍵
    document.addEventListener('keydown', function(event) {
        if (event.ctrlKey && event.shiftKey) {
            switch (event.key) {
                case 'D':
                    // Ctrl+Shift+D 顯示調試信息
                    event.preventDefault();
                    console.log('遊戲狀態:', game.getGameState());
                    console.log('玩家手牌:', game.players.map(p => p.hand.map(c => c.toString())));
                    break;
                    
                case 'W':
                    // Ctrl+Shift+W 讓玩家1獲勝（調試用）
                    event.preventDefault();
                    game.players[0].hand = [game.players[0].hand[0]]; // 只留一張牌
                    ui.updateUI();
                    break;
            }
        }
    });
    
    // 將遊戲對象暴露到全局作用域以便調試
    window.debugGame = game;
    window.debugUI = ui;
}

/**
 * 性能監控
 */
if ('performance' in window) {
    window.addEventListener('load', function() {
        setTimeout(function() {
            const perfData = performance.timing;
            const loadTime = perfData.loadEventEnd - perfData.navigationStart;
            console.log(`頁面加載時間: ${loadTime}ms`);
        }, 0);
    });
}

// 導出到全局作用域（如果需要）
window.BigTwoGame = BigTwoGame;
window.UIController = UIController;
