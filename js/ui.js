/**
 * UI控制器
 * 管理用戶界面的更新和交互
 */
class UIController {
    constructor(game) {
        this.game = game;
        this.selectedCards = new Set(); // 選中的牌的索引
        this.initializeElements();
        this.bindEvents();
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        this.elements = {
            currentPlayer: document.getElementById('current-player'),
            gameStatus: document.getElementById('game-status'),
            player1Cards: document.getElementById('player1-cards'),
            player2Cards: document.getElementById('player2-cards'),
            player3Cards: document.getElementById('player3-cards'),
            player4Cards: document.getElementById('player4-cards'),
            player2Count: document.getElementById('player2-count'),
            player3Count: document.getElementById('player3-count'),
            player4Count: document.getElementById('player4-count'),
            lastPlayedCards: document.getElementById('last-played-cards'),
            lastPlayer: document.getElementById('last-player'),
            playCardsBtn: document.getElementById('play-cards-btn'),
            passBtn: document.getElementById('pass-btn'),
            newGameBtn: document.getElementById('new-game-btn'),
            rulesPanel: document.getElementById('rules-panel'),
            showRulesBtn: document.getElementById('show-rules'),
            closeRulesBtn: document.getElementById('close-rules')
        };
    }

    /**
     * 綁定事件監聽器
     */
    bindEvents() {
        // 遊戲控制按鈕
        this.elements.playCardsBtn.addEventListener('click', () => this.playSelectedCards());
        this.elements.passBtn.addEventListener('click', () => this.pass());
        this.elements.newGameBtn.addEventListener('click', () => this.newGame());

        // 規則面板
        this.elements.showRulesBtn.addEventListener('click', () => this.showRules());
        this.elements.closeRulesBtn.addEventListener('click', () => this.hideRules());

        // 點擊遊戲區域外關閉規則面板
        document.addEventListener('click', (e) => {
            if (e.target === this.elements.rulesPanel) {
                this.hideRules();
            }
        });
    }

    /**
     * 更新整個遊戲界面
     */
    updateUI() {
        const gameState = this.game.getGameState();
        
        this.updateGameInfo(gameState);
        this.updatePlayerCards(gameState);
        this.updateLastPlay(gameState);
        this.updateButtons(gameState);
    }

    /**
     * 更新遊戲信息
     */
    updateGameInfo(gameState) {
        const currentPlayerName = gameState.players[gameState.currentPlayer].name;
        this.elements.currentPlayer.textContent = currentPlayerName;

        let status = '';
        switch (gameState.gameState) {
            case 'waiting':
                status = '等待開始';
                break;
            case 'playing':
                if (gameState.isFirstRound) {
                    status = '第一輪 - 必須包含♦3';
                } else {
                    status = '遊戲進行中';
                }
                break;
            case 'finished':
                const winner = gameState.players[gameState.winner];
                status = `遊戲結束 - ${winner.name}獲勝！`;
                break;
        }
        this.elements.gameStatus.textContent = status;
    }

    /**
     * 更新玩家手牌顯示
     */
    updatePlayerCards(gameState) {
        // 更新玩家1的手牌（顯示具體牌面）
        this.updatePlayer1Cards();

        // 更新其他玩家的牌數
        this.elements.player2Count.textContent = gameState.players[1].cardCount;
        this.elements.player3Count.textContent = gameState.players[2].cardCount;
        this.elements.player4Count.textContent = gameState.players[3].cardCount;

        // 更新其他玩家的牌背
        this.updatePlayerBackCards(this.elements.player2Cards, gameState.players[1].cardCount);
        this.updatePlayerBackCards(this.elements.player3Cards, gameState.players[2].cardCount);
        this.updatePlayerBackCards(this.elements.player4Cards, gameState.players[3].cardCount);
    }

    /**
     * 更新玩家1的手牌
     */
    updatePlayer1Cards() {
        const player1 = this.game.players[0];
        this.elements.player1Cards.innerHTML = '';
        this.selectedCards.clear();

        player1.hand.forEach((card, index) => {
            const cardElement = card.createElement();
            cardElement.dataset.cardIndex = index;
            
            // 添加點擊事件
            cardElement.addEventListener('click', () => this.toggleCardSelection(index, cardElement));
            
            this.elements.player1Cards.appendChild(cardElement);
        });
    }

    /**
     * 更新其他玩家的牌背
     */
    updatePlayerBackCards(container, cardCount) {
        container.innerHTML = '';
        
        for (let i = 0; i < cardCount; i++) {
            const cardBack = document.createElement('div');
            cardBack.className = 'card-back';
            container.appendChild(cardBack);
        }
    }

    /**
     * 切換牌的選中狀態
     */
    toggleCardSelection(index, cardElement) {
        if (this.game.getCurrentPlayer().isAI) {
            return; // AI回合不允許選牌
        }

        if (this.selectedCards.has(index)) {
            this.selectedCards.delete(index);
            cardElement.classList.remove('selected');
        } else {
            this.selectedCards.add(index);
            cardElement.classList.add('selected');
        }

        this.updateButtons();
    }

    /**
     * 更新上一手出牌顯示
     */
    updateLastPlay(gameState) {
        if (gameState.lastPlay) {
            this.elements.lastPlayedCards.innerHTML = '';
            
            gameState.lastPlay.cards.forEach(card => {
                const cardElement = card.createElement();
                cardElement.style.transform = 'scale(0.8)';
                this.elements.lastPlayedCards.appendChild(cardElement);
            });

            const playerName = gameState.players[gameState.lastPlay.player].name;
            this.elements.lastPlayer.textContent = playerName;
        } else {
            this.elements.lastPlayedCards.innerHTML = '<div style="color: #ccc;">暫無</div>';
            this.elements.lastPlayer.textContent = '-';
        }
    }

    /**
     * 更新按鈕狀態
     */
    updateButtons(gameState = null) {
        if (!gameState) {
            gameState = this.game.getGameState();
        }

        const isPlayerTurn = gameState.currentPlayer === 0 && gameState.gameState === 'playing';
        const hasSelectedCards = this.selectedCards.size > 0;
        const canPass = !gameState.isFirstRound && gameState.lastPlay;

        this.elements.playCardsBtn.disabled = !isPlayerTurn || !hasSelectedCards;
        this.elements.passBtn.disabled = !isPlayerTurn || !canPass;
    }

    /**
     * 出選中的牌
     */
    playSelectedCards() {
        if (this.selectedCards.size === 0) {
            this.showMessage('請選擇要出的牌');
            return;
        }

        const cardIndices = Array.from(this.selectedCards).sort((a, b) => a - b);
        const result = this.game.playCards(0, cardIndices);

        if (result.success) {
            this.showMessage(result.message);
            this.updateUI();
            
            if (result.gameOver) {
                this.showGameOverMessage(result.winner);
            } else {
                // 如果下一個是AI，自動執行AI回合
                this.handleAITurn();
            }
        } else {
            this.showMessage(result.message, 'error');
        }
    }

    /**
     * Pass
     */
    pass() {
        const result = this.game.pass(0);
        
        if (result.success) {
            this.showMessage(result.message);
            this.updateUI();
            this.handleAITurn();
        } else {
            this.showMessage(result.message, 'error');
        }
    }

    /**
     * 開始新遊戲
     */
    newGame() {
        this.game.restart();
        this.updateUI();
        this.showMessage('新遊戲開始！');
        
        // 如果第一個玩家是AI，開始AI回合
        this.handleAITurn();
    }

    /**
     * 處理AI回合
     */
    async handleAITurn() {
        const gameState = this.game.getGameState();
        
        if (gameState.gameState === 'playing' && gameState.players[gameState.currentPlayer].isAI) {
            this.showMessage('AI思考中...', 'info');
            
            const result = await this.game.aiMove();
            
            if (result) {
                if (result.gameOver) {
                    this.showGameOverMessage(result.winner);
                } else {
                    this.showMessage(`${this.game.players[gameState.currentPlayer].name}: ${result.message}`);
                }
                
                this.updateUI();
                
                // 繼續下一個AI回合
                setTimeout(() => this.handleAITurn(), 500);
            }
        }
    }

    /**
     * 顯示消息
     */
    showMessage(message, type = 'info') {
        // 創建消息元素
        const messageElement = document.createElement('div');
        messageElement.className = `message message-${type}`;
        messageElement.textContent = message;
        messageElement.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transition: opacity 0.3s ease;
        `;

        // 設置顏色
        switch (type) {
            case 'error':
                messageElement.style.backgroundColor = '#dc3545';
                break;
            case 'success':
                messageElement.style.backgroundColor = '#28a745';
                break;
            default:
                messageElement.style.backgroundColor = '#17a2b8';
        }

        document.body.appendChild(messageElement);

        // 3秒後移除
        setTimeout(() => {
            messageElement.style.opacity = '0';
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.parentNode.removeChild(messageElement);
                }
            }, 300);
        }, 3000);
    }

    /**
     * 顯示遊戲結束消息
     */
    showGameOverMessage(winnerIndex) {
        const winner = this.game.players[winnerIndex];
        this.showMessage(`🎉 ${winner.name}獲勝！🎉`, 'success');
    }

    /**
     * 顯示規則面板
     */
    showRules() {
        this.elements.rulesPanel.classList.add('show');
    }

    /**
     * 隱藏規則面板
     */
    hideRules() {
        this.elements.rulesPanel.classList.remove('show');
    }
}
