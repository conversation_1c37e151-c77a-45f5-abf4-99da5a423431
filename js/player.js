/**
 * 玩家類別
 * 管理玩家的手牌、出牌邏輯等
 */
class Player {
    constructor(name, isAI = false) {
        this.name = name;
        this.isAI = isAI;
        this.hand = []; // 手牌
        this.isActive = true; // 是否還在遊戲中
    }

    /**
     * 接收發牌
     */
    receiveCards(cards) {
        this.hand.push(...cards);
        this.sortHand();
    }

    /**
     * 接收單張牌
     */
    receiveCard(card) {
        this.hand.push(card);
        this.sortHand();
    }

    /**
     * 排序手牌
     */
    sortHand() {
        this.hand.sort((a, b) => a.compareTo(b));
    }

    /**
     * 出牌
     */
    playCards(cardIndices) {
        const playedCards = [];
        // 從大到小排序索引，避免移除時索引錯亂
        const sortedIndices = [...cardIndices].sort((a, b) => b - a);
        
        for (const index of sortedIndices) {
            if (index >= 0 && index < this.hand.length) {
                playedCards.unshift(this.hand.splice(index, 1)[0]);
            }
        }
        
        return playedCards;
    }

    /**
     * 檢查是否有♦3
     */
    hasDiamondThree() {
        return this.hand.some(card => card.isDiamondThree());
    }

    /**
     * 獲取♦3的索引
     */
    getDiamondThreeIndex() {
        return this.hand.findIndex(card => card.isDiamondThree());
    }

    /**
     * 檢查手牌是否為空
     */
    hasNoCards() {
        return this.hand.length === 0;
    }

    /**
     * 獲取手牌數量
     */
    getCardCount() {
        return this.hand.length;
    }

    /**
     * 檢查是否可以出指定的牌
     */
    canPlayCards(cardIndices) {
        // 檢查索引是否有效
        for (const index of cardIndices) {
            if (index < 0 || index >= this.hand.length) {
                return false;
            }
        }

        // 獲取要出的牌
        const cards = cardIndices.map(index => this.hand[index]);
        
        // 分析牌型
        const handAnalysis = HandAnalyzer.analyzeHand(cards);
        
        return handAnalysis.type !== 'invalid';
    }

    /**
     * 重置玩家狀態
     */
    reset() {
        this.hand = [];
        this.isActive = true;
    }
}

/**
 * AI玩家類別
 * 繼承Player，添加AI邏輯
 */
class AIPlayer extends Player {
    constructor(name) {
        super(name, true);
        this.difficulty = 'medium'; // easy, medium, hard
    }

    /**
     * AI決定出牌
     */
    makeMove(lastPlay, gameState) {
        if (!lastPlay) {
            // 場面清空，可以出任意牌
            if (gameState.isFirstRound && gameState.isGameStart && this.hasDiamondThree()) {
                // 真正的第一輪，必須包含♦3
                return this.playFirstHand();
            } else {
                // 場面清空後的新開始，可以出任意牌
                return this.playAnyCard();
            }
        }

        // 嘗試跟牌
        const response = this.findValidResponse(lastPlay);
        if (response) {
            return {
                action: 'play',
                cards: response
            };
        }

        // 無法跟牌，選擇pass
        return {
            action: 'pass'
        };
    }

    /**
     * 出第一手牌（包含♦3）
     */
    playFirstHand() {
        const diamondThreeIndex = this.getDiamondThreeIndex();
        if (diamondThreeIndex === -1) {
            throw new Error('AI玩家沒有♦3');
        }

        // 簡單策略：只出♦3
        return {
            action: 'play',
            cards: [diamondThreeIndex]
        };
    }

    /**
     * 出任意牌（場面清空後）
     */
    playAnyCard() {
        if (this.hand.length === 0) {
            throw new Error('AI玩家沒有手牌');
        }

        // 簡單策略：出最小的單張
        return {
            action: 'play',
            cards: [0] // 手牌已排序，第一張是最小的
        };
    }

    /**
     * 尋找有效的跟牌
     */
    findValidResponse(lastPlay) {
        const lastAnalysis = HandAnalyzer.analyzeHand(lastPlay.cards);
        const cardCount = lastPlay.cards.length;

        // 根據上一手牌的類型尋找對應的牌
        switch (lastAnalysis.type) {
            case 'single':
                return this.findBetterSingle(lastPlay.cards[0]);
            case 'pair':
                return this.findBetterPair(lastPlay.cards);
            case 'triple':
                return this.findBetterTriple(lastPlay.cards);
            case 'straight':
            case 'flush':
            case 'full_house':
            case 'four_of_a_kind':
            case 'straight_flush':
                return this.findBetterFiveCardHand(lastPlay.cards, lastAnalysis.type);
            default:
                return null;
        }
    }

    /**
     * 尋找更大的單張
     */
    findBetterSingle(lastCard) {
        for (let i = 0; i < this.hand.length; i++) {
            if (this.hand[i].isGreaterThan(lastCard)) {
                return [i];
            }
        }
        return null;
    }

    /**
     * 尋找更大的對子
     */
    findBetterPair(lastCards) {
        const lastRank = lastCards[0].getBigTwoRank();
        const lastMaxCard = lastCards[lastCards.length - 1];

        for (let i = 0; i < this.hand.length - 1; i++) {
            for (let j = i + 1; j < this.hand.length; j++) {
                const card1 = this.hand[i];
                const card2 = this.hand[j];

                if (card1.getBigTwoRank() === card2.getBigTwoRank()) {
                    const maxCard = card1.isGreaterThan(card2) ? card1 : card2;
                    if (maxCard.isGreaterThan(lastMaxCard)) {
                        return [i, j];
                    }
                }
            }
        }
        return null;
    }

    /**
     * 尋找更大的三條
     */
    findBetterTriple(lastCards) {
        const lastRank = lastCards[0].getBigTwoRank();
        const lastMaxCard = lastCards[lastCards.length - 1];

        // 計算每個點數的牌數
        const rankCounts = {};
        const rankIndices = {};

        for (let i = 0; i < this.hand.length; i++) {
            const rank = this.hand[i].getBigTwoRank();
            if (!rankCounts[rank]) {
                rankCounts[rank] = 0;
                rankIndices[rank] = [];
            }
            rankCounts[rank]++;
            rankIndices[rank].push(i);
        }

        // 尋找三條
        for (const rank in rankCounts) {
            if (rankCounts[rank] >= 3) {
                const indices = rankIndices[rank].slice(0, 3);
                const maxCard = this.hand[indices[indices.length - 1]];
                if (maxCard.isGreaterThan(lastMaxCard)) {
                    return indices;
                }
            }
        }

        return null;
    }

    /**
     * 尋找更大的五張牌組合
     */
    findBetterFiveCardHand(lastCards, lastType) {
        // 簡化實現：隨機嘗試五張牌的組合
        // 實際實現中應該更智能地搜索
        const combinations = this.generateFiveCardCombinations();
        
        for (const combo of combinations) {
            const cards = combo.map(index => this.hand[index]);
            const analysis = HandAnalyzer.analyzeHand(cards);
            
            if (analysis.type !== 'invalid') {
                const comparison = HandAnalyzer.compareHands(
                    { cards: lastCards },
                    { cards: cards }
                );
                
                if (comparison < 0) {
                    return combo;
                }
            }
        }
        
        return null;
    }

    /**
     * 生成五張牌的組合（簡化版本）
     */
    generateFiveCardCombinations() {
        const combinations = [];
        const handSize = this.hand.length;
        
        if (handSize < 5) return combinations;
        
        // 只生成前幾個組合，避免計算過多
        const maxCombinations = Math.min(10, this.getCombinationCount(handSize, 5));
        
        for (let i = 0; i < handSize - 4 && combinations.length < maxCombinations; i++) {
            for (let j = i + 1; j < handSize - 3 && combinations.length < maxCombinations; j++) {
                for (let k = j + 1; k < handSize - 2 && combinations.length < maxCombinations; k++) {
                    for (let l = k + 1; l < handSize - 1 && combinations.length < maxCombinations; l++) {
                        for (let m = l + 1; m < handSize && combinations.length < maxCombinations; m++) {
                            combinations.push([i, j, k, l, m]);
                        }
                    }
                }
            }
        }
        
        return combinations;
    }

    /**
     * 計算組合數
     */
    getCombinationCount(n, r) {
        if (r > n) return 0;
        if (r === 0 || r === n) return 1;
        
        let result = 1;
        for (let i = 0; i < r; i++) {
            result = result * (n - i) / (i + 1);
        }
        return Math.floor(result);
    }
}
