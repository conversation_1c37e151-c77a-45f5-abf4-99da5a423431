/**
 * 大老二遊戲主類別
 * 管理遊戲狀態、規則驗證、回合控制等
 */
class BigTwoGame {
    constructor() {
        this.players = [];
        this.deck = new Deck();
        this.currentPlayerIndex = 0;
        this.lastPlay = null; // 上一手出的牌
        this.lastPlayerIndex = -1; // 上一個出牌的玩家
        this.gameState = 'waiting'; // waiting, playing, finished
        this.winner = null;
        this.passCount = 0; // 連續pass的次數
        this.isFirstRound = true; // 是否為第一輪
        this.isGameStart = true; // 是否為遊戲開始（真正的第一手）
    }

    /**
     * 初始化遊戲
     */
    initGame() {
        // 創建四個玩家
        this.players = [
            new Player('玩家1', false),
            new AIPlayer('玩家2'),
            new AIPlayer('玩家3'),
            new AIPlayer('玩家4')
        ];

        // 重置牌組
        this.deck.reset();
        
        // 發牌
        this.dealCards();
        
        // 找到有♦3的玩家開始
        this.findStartingPlayer();
        
        // 設置遊戲狀態
        this.gameState = 'playing';
        this.isFirstRound = true;
        this.isGameStart = true;
        this.lastPlay = null;
        this.passCount = 0;
        this.winner = null;
    }

    /**
     * 發牌給所有玩家
     */
    dealCards() {
        // 每個玩家發13張牌
        for (let i = 0; i < 13; i++) {
            for (const player of this.players) {
                player.receiveCard(this.deck.dealCard());
            }
        }
    }

    /**
     * 找到有♦3的玩家作為起始玩家
     */
    findStartingPlayer() {
        for (let i = 0; i < this.players.length; i++) {
            if (this.players[i].hasDiamondThree()) {
                this.currentPlayerIndex = i;
                break;
            }
        }
    }

    /**
     * 獲取當前玩家
     */
    getCurrentPlayer() {
        return this.players[this.currentPlayerIndex];
    }

    /**
     * 玩家出牌
     */
    playCards(playerIndex, cardIndices) {
        if (this.gameState !== 'playing') {
            return { success: false, message: '遊戲未開始' };
        }

        if (playerIndex !== this.currentPlayerIndex) {
            return { success: false, message: '不是你的回合' };
        }

        const player = this.players[playerIndex];
        
        // 驗證出牌是否有效
        const validation = this.validatePlay(player, cardIndices);
        if (!validation.valid) {
            return { success: false, message: validation.message };
        }

        // 執行出牌
        const playedCards = player.playCards(cardIndices);
        
        // 更新遊戲狀態
        this.lastPlay = {
            player: playerIndex,
            cards: playedCards,
            analysis: HandAnalyzer.analyzeHand(playedCards)
        };
        this.lastPlayerIndex = playerIndex;
        this.passCount = 0;
        this.isFirstRound = false;
        this.isGameStart = false;

        // 檢查是否獲勝
        if (player.hasNoCards()) {
            this.gameState = 'finished';
            this.winner = playerIndex;
            return { 
                success: true, 
                message: `${player.name}獲勝！`,
                gameOver: true,
                winner: playerIndex
            };
        }

        // 切換到下一個玩家
        this.nextPlayer();

        return { 
            success: true, 
            message: '出牌成功',
            playedCards: playedCards,
            nextPlayer: this.currentPlayerIndex
        };
    }

    /**
     * 玩家選擇pass
     */
    pass(playerIndex) {
        if (this.gameState !== 'playing') {
            return { success: false, message: '遊戲未開始' };
        }

        if (playerIndex !== this.currentPlayerIndex) {
            return { success: false, message: '不是你的回合' };
        }

        if (this.isFirstRound) {
            return { success: false, message: '第一輪不能pass' };
        }

        if (!this.lastPlay) {
            return { success: false, message: '沒有上一手牌，不能pass' };
        }

        this.passCount++;

        // 如果連續三個玩家pass，清空場面
        if (this.passCount >= 3) {
            this.lastPlay = null;
            this.passCount = 0;
            this.isFirstRound = true;
            // 注意：這裡不設置isGameStart = true，因為這不是遊戲開始
            // 由最後出牌的玩家重新開始
            this.currentPlayerIndex = this.lastPlayerIndex;
        } else {
            this.nextPlayer();
        }

        return { 
            success: true, 
            message: 'Pass成功',
            nextPlayer: this.currentPlayerIndex
        };
    }

    /**
     * 驗證出牌是否有效
     */
    validatePlay(player, cardIndices) {
        // 檢查牌的索引是否有效
        if (!player.canPlayCards(cardIndices)) {
            return { valid: false, message: '無效的牌型' };
        }

        const cards = cardIndices.map(index => player.hand[index]);

        // 只有遊戲真正開始的第一手牌才必須包含♦3
        if (this.isFirstRound && this.isGameStart) {
            const hasDiamondThree = cards.some(card => card.isDiamondThree());
            if (!hasDiamondThree) {
                return { valid: false, message: '第一手牌必須包含♦3' };
            }
        }

        // 如果有上一手牌，需要比較大小
        if (this.lastPlay && !this.isFirstRound) {
            const currentAnalysis = HandAnalyzer.analyzeHand(cards);
            const lastAnalysis = this.lastPlay.analysis;

            // 牌數必須相同
            if (cards.length !== this.lastPlay.cards.length) {
                return { valid: false, message: '出牌數量必須與上一手相同' };
            }

            // 牌型必須相同或更大
            const comparison = HandAnalyzer.compareHands(
                { cards: this.lastPlay.cards },
                { cards: cards }
            );

            if (comparison >= 0) {
                return { valid: false, message: '出的牌必須比上一手大' };
            }
        }

        return { valid: true };
    }

    /**
     * 切換到下一個玩家
     */
    nextPlayer() {
        do {
            this.currentPlayerIndex = (this.currentPlayerIndex + 1) % this.players.length;
        } while (!this.players[this.currentPlayerIndex].isActive);
    }

    /**
     * 獲取遊戲狀態
     */
    getGameState() {
        return {
            gameState: this.gameState,
            currentPlayer: this.currentPlayerIndex,
            lastPlay: this.lastPlay,
            players: this.players.map((player, index) => ({
                name: player.name,
                cardCount: player.getCardCount(),
                isAI: player.isAI,
                isActive: player.isActive,
                isCurrent: index === this.currentPlayerIndex
            })),
            winner: this.winner,
            isFirstRound: this.isFirstRound,
            isGameStart: this.isGameStart
        };
    }

    /**
     * 重新開始遊戲
     */
    restart() {
        // 重置所有玩家
        for (const player of this.players) {
            player.reset();
        }

        // 重置遊戲狀態
        this.deck.reset();
        this.currentPlayerIndex = 0;
        this.lastPlay = null;
        this.lastPlayerIndex = -1;
        this.gameState = 'waiting';
        this.winner = null;
        this.passCount = 0;
        this.isFirstRound = true;
        this.isGameStart = true;

        // 重新初始化
        this.initGame();
    }

    /**
     * AI玩家自動出牌
     */
    async aiMove() {
        if (this.gameState !== 'playing') {
            return;
        }

        const currentPlayer = this.getCurrentPlayer();
        if (!currentPlayer.isAI) {
            return;
        }

        // 延遲一下讓玩家看到AI思考
        await new Promise(resolve => setTimeout(resolve, 1000));

        const move = currentPlayer.makeMove(this.lastPlay, this.getGameState());

        if (move.action === 'play') {
            return this.playCards(this.currentPlayerIndex, move.cards);
        } else if (move.action === 'pass') {
            return this.pass(this.currentPlayerIndex);
        }
    }

    /**
     * 檢查遊戲是否結束
     */
    isGameOver() {
        return this.gameState === 'finished';
    }

    /**
     * 獲取獲勝者
     */
    getWinner() {
        if (this.winner !== null) {
            return this.players[this.winner];
        }
        return null;
    }
}
