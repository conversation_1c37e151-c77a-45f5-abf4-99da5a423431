/**
 * 牌組類別
 * 管理一副撲克牌的洗牌、發牌等操作
 */
class Deck {
    constructor() {
        this.cards = createStandardDeck();
        this.shuffle();
    }

    /**
     * 洗牌 - 使用Fisher-Yates洗牌算法
     */
    shuffle() {
        for (let i = this.cards.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.cards[i], this.cards[j]] = [this.cards[j], this.cards[i]];
        }
    }

    /**
     * 發一張牌
     */
    dealCard() {
        if (this.cards.length === 0) {
            throw new Error('牌組已空，無法發牌');
        }
        return this.cards.pop();
    }

    /**
     * 發多張牌
     */
    dealCards(count) {
        const dealtCards = [];
        for (let i = 0; i < count; i++) {
            if (this.cards.length > 0) {
                dealtCards.push(this.dealCard());
            }
        }
        return dealtCards;
    }

    /**
     * 獲取剩餘牌數
     */
    getCardCount() {
        return this.cards.length;
    }

    /**
     * 檢查牌組是否為空
     */
    isEmpty() {
        return this.cards.length === 0;
    }

    /**
     * 重置牌組
     */
    reset() {
        this.cards = createStandardDeck();
        this.shuffle();
    }

    /**
     * 查看頂部的牌（不移除）
     */
    peek() {
        if (this.cards.length === 0) {
            return null;
        }
        return this.cards[this.cards.length - 1];
    }

    /**
     * 將牌加回牌組底部
     */
    addCard(card) {
        this.cards.unshift(card);
    }

    /**
     * 將多張牌加回牌組底部
     */
    addCards(cards) {
        this.cards.unshift(...cards);
    }
}

/**
 * 牌型分析工具類別
 */
class HandAnalyzer {
    /**
     * 分析手牌類型
     */
    static analyzeHand(cards) {
        if (!cards || cards.length === 0) {
            return { type: 'empty', cards: [] };
        }

        // 按點數排序
        const sortedCards = [...cards].sort((a, b) => a.compareTo(b));

        switch (cards.length) {
            case 1:
                return { type: 'single', cards: sortedCards };
            case 2:
                return this.analyzePair(sortedCards);
            case 3:
                return this.analyzeTriple(sortedCards);
            case 5:
                return this.analyzeFiveCards(sortedCards);
            default:
                return { type: 'invalid', cards: sortedCards };
        }
    }

    /**
     * 分析對子
     */
    static analyzePair(cards) {
        if (cards.length !== 2) {
            return { type: 'invalid', cards };
        }

        if (cards[0].getBigTwoRank() === cards[1].getBigTwoRank()) {
            return { type: 'pair', cards };
        }

        return { type: 'invalid', cards };
    }

    /**
     * 分析三條
     */
    static analyzeTriple(cards) {
        if (cards.length !== 3) {
            return { type: 'invalid', cards };
        }

        const rank = cards[0].getBigTwoRank();
        if (cards.every(card => card.getBigTwoRank() === rank)) {
            return { type: 'triple', cards };
        }

        return { type: 'invalid', cards };
    }

    /**
     * 分析五張牌的牌型
     */
    static analyzeFiveCards(cards) {
        if (cards.length !== 5) {
            return { type: 'invalid', cards };
        }

        const isFlush = this.isFlush(cards);
        const isStraight = this.isStraight(cards);
        const rankCounts = this.getRankCounts(cards);
        const counts = Object.values(rankCounts).sort((a, b) => b - a);

        if (isFlush && isStraight) {
            return { type: 'straight_flush', cards };
        }

        if (counts[0] === 4) {
            return { type: 'four_of_a_kind', cards };
        }

        if (counts[0] === 3 && counts[1] === 2) {
            return { type: 'full_house', cards };
        }

        if (isFlush) {
            return { type: 'flush', cards };
        }

        if (isStraight) {
            return { type: 'straight', cards };
        }

        return { type: 'invalid', cards };
    }

    /**
     * 檢查是否為同花
     */
    static isFlush(cards) {
        const suit = cards[0].suit;
        return cards.every(card => card.suit === suit);
    }

    /**
     * 檢查是否為順子
     */
    static isStraight(cards) {
        const ranks = cards.map(card => card.getBigTwoRank()).sort((a, b) => a - b);
        
        // 檢查連續性
        for (let i = 1; i < ranks.length; i++) {
            if (ranks[i] !== ranks[i - 1] + 1) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 獲取點數計數
     */
    static getRankCounts(cards) {
        const counts = {};
        for (const card of cards) {
            const rank = card.getBigTwoRank();
            counts[rank] = (counts[rank] || 0) + 1;
        }
        return counts;
    }

    /**
     * 比較兩手牌的大小
     */
    static compareHands(hand1, hand2) {
        const analysis1 = this.analyzeHand(hand1.cards);
        const analysis2 = this.analyzeHand(hand2.cards);

        // 牌型優先級
        const typeRanks = {
            'single': 1,
            'pair': 2,
            'triple': 3,
            'straight': 4,
            'flush': 5,
            'full_house': 6,
            'four_of_a_kind': 7,
            'straight_flush': 8
        };

        const rank1 = typeRanks[analysis1.type] || 0;
        const rank2 = typeRanks[analysis2.type] || 0;

        if (rank1 !== rank2) {
            return rank1 - rank2;
        }

        // 同牌型比較最大牌
        const maxCard1 = this.getMaxCard(analysis1);
        const maxCard2 = this.getMaxCard(analysis2);

        return maxCard1.compareTo(maxCard2);
    }

    /**
     * 獲取手牌中的最大牌
     */
    static getMaxCard(handAnalysis) {
        return handAnalysis.cards[handAnalysis.cards.length - 1];
    }
}
