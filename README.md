# 大老二撲克牌遊戲

一個完整的大老二撲克牌遊戲，使用純JavaScript實現，支持1個人類玩家對戰3個AI玩家。

## 功能特色

- 🎮 完整的大老二遊戲規則實現
- 🤖 智能AI對手
- 🎨 美觀的用戶界面
- 📱 響應式設計，支持手機和桌面
- ⌨️ 鍵盤快捷鍵支持
- 🎯 實時遊戲狀態顯示

## 遊戲規則

### 基本規則
- 使用標準52張撲克牌
- 4個玩家，每人13張牌
- 牌的大小順序：3 < 4 < 5 < 6 < 7 < 8 < 9 < 10 < J < Q < K < A < 2
- 花色大小順序：♦ < ♣ < ♥ < ♠

### 牌型
1. **單張** - 任意一張牌
2. **對子** - 兩張相同點數的牌
3. **三條** - 三張相同點數的牌
4. **順子** - 五張連續點數的牌
5. **同花** - 五張相同花色的牌
6. **葫蘆** - 三條+對子
7. **四條** - 四張相同點數的牌+一張任意牌
8. **同花順** - 五張連續點數且相同花色的牌

### 遊戲流程
1. 持有♦3的玩家先出牌，第一手必須包含♦3
2. 其他玩家按順序出牌，必須出相同張數且更大的牌型
3. 無法出牌時可以選擇Pass
4. 連續三個玩家Pass後，場面清空，由最後出牌的玩家重新開始
5. 最先出完所有手牌的玩家獲勝

## 如何運行

### 方法1：直接打開HTML文件
1. 下載所有文件到本地目錄
2. 用瀏覽器直接打開 `index.html`

### 方法2：使用本地服務器（推薦）
1. 確保已安裝Python
2. 在項目目錄中運行：
   ```bash
   python3 -m http.server 8000
   ```
3. 在瀏覽器中訪問：`http://localhost:8000`

### 方法3：使用Node.js服務器
1. 安裝http-server：
   ```bash
   npm install -g http-server
   ```
2. 在項目目錄中運行：
   ```bash
   http-server
   ```

## 操作說明

### 鼠標操作
- **點擊牌** - 選擇/取消選擇手牌
- **出牌按鈕** - 出選中的牌
- **Pass按鈕** - 跳過這一輪
- **新遊戲按鈕** - 開始新遊戲
- **遊戲規則按鈕** - 查看詳細規則

### 鍵盤快捷鍵
- **Enter** - 出選中的牌
- **Space** - Pass
- **Escape** - 取消所有選擇
- **H** 或 **?** - 顯示遊戲規則
- **Ctrl+R** - 重新開始遊戲

## 文件結構

```
大老二遊戲/
├── index.html          # 主遊戲頁面
├── styles.css          # 樣式文件
├── test.html           # 測試頁面
├── README.md           # 說明文件
└── js/
    ├── card.js         # 撲克牌類別
    ├── deck.js         # 牌組和牌型分析
    ├── player.js       # 玩家和AI邏輯
    ├── game.js         # 遊戲核心邏輯
    ├── ui.js           # 用戶界面控制
    └── main.js         # 主程序入口
```

## 技術實現

### 核心類別
- **Card** - 表示單張撲克牌
- **Deck** - 管理牌組和洗牌
- **HandAnalyzer** - 分析牌型和比較大小
- **Player** - 玩家基礎類別
- **AIPlayer** - AI玩家邏輯
- **BigTwoGame** - 遊戲主控制器
- **UIController** - 用戶界面管理

### AI策略
- 基於規則的AI決策
- 智能牌型識別和選擇
- 動態難度調整（可擴展）

## 測試

訪問 `test.html` 頁面可以運行自動化測試：
- 基礎功能測試
- 牌型分析測試
- 遊戲邏輯測試
- 快速遊戲演示

## 瀏覽器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 開發和調試

### 調試模式
在localhost環境下，可以使用以下快捷鍵：
- **Ctrl+Shift+D** - 顯示遊戲狀態
- **Ctrl+Shift+W** - 快速獲勝（調試用）

### 控制台命令
```javascript
// 查看遊戲狀態
debugGame.getGameState()

// 查看所有玩家手牌
debugGame.players.map(p => p.hand.map(c => c.toString()))
```

## 未來改進

- [ ] 多人在線對戰
- [ ] 更高級的AI算法
- [ ] 遊戲統計和排行榜
- [ ] 自定義遊戲規則
- [ ] 音效和動畫效果
- [ ] 移動端優化

## 許可證

MIT License - 可自由使用和修改

## 貢獻

歡迎提交Issue和Pull Request來改進這個遊戲！

---

享受遊戲！🎉
